<template>
  <div
    class="flow-editor-demo"
    style="
      width: 100vw;
      height: 100vh;
      display: flex;
      flex-direction: column;
      background-color: #f3f4f6;
    "
  >
    <!-- 顶部标题栏 -->
    <div class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">流程编辑器演示</h1>
          <p class="text-sm text-gray-600 mt-1">基于X6的可视化流程编辑器</p>
        </div>

        <div class="flex items-center space-x-4">
          <!-- 示例数据按钮 -->
          <button
            @click="loadSampleData"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i class="pi pi-play mr-2" />
            加载示例
          </button>

          <!-- 表格数据模拟 -->
          <button
            @click="showTableData = !showTableData"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <i class="pi pi-table mr-2" />
            表格数据
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div style="flex: 1; display: flex">
      <!-- 左侧表格数据面板 -->
      <div
        v-if="showTableData"
        class="w-80 bg-white border-r border-gray-200 flex flex-col"
      >
        <div class="p-4 border-b border-gray-200">
          <h3 class="font-semibold text-gray-800">业务数据表格</h3>
          <p class="text-sm text-gray-600 mt-1">拖拽行到画布创建节点</p>
        </div>

        <div class="flex-1 overflow-y-auto p-4">
          <div class="space-y-2">
            <div
              v-for="(item, index) in tableData"
              :key="index"
              :draggable="true"
              @dragstart="handleTableRowDrag(item, $event)"
              class="p-3 bg-gray-50 border border-gray-200 rounded-lg cursor-move hover:bg-gray-100 hover:border-blue-300 transition-all"
            >
              <div class="font-medium text-gray-800">{{ item.name }}</div>
              <div class="text-sm text-gray-600">{{ item.description }}</div>
              <div class="text-xs text-gray-500 mt-1">
                类型: {{ item.type }} | 分类: {{ item.category }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 流程编辑器 -->
      <div class="flex-1">
        <SimpleFlowEditor />
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="bg-white border-t border-gray-200 px-6 py-2">
      <div class="flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center space-x-4">
          <span>节点: {{ nodeCount }}</span>
          <span>连线: {{ edgeCount }}</span>
          <span v-if="validationResults.length > 0" class="text-red-600">
            验证问题: {{ validationResults.length }}
          </span>
        </div>

        <div class="flex items-center space-x-4">
          <span>{{ flowData ? "已保存" : "未保存" }}</span>
          <span>{{ new Date().toLocaleTimeString() }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import SimpleFlowEditor from "../SimpleFlowEditor.vue";
import type {
  FlowNodeType,
  FlowNode,
  FlowEdge,
  ValidationResult,
} from "../types";
import { nodeTypeRegistry } from "../nodes/simpleNodeTypes";

// 响应式数据
const flowData = ref<any>(null);
const showTableData = ref(true);
const validationResults = ref<ValidationResult[]>([]);

// 自定义节点类型
const customNodeTypes = ref<FlowNodeType[]>([
  ...nodeTypeRegistry.getAll(),
  // 可以添加自定义节点类型
  {
    id: "custom-approval",
    name: "审批节点",
    category: "process",
    icon: "pi pi-check-square",
    color: "#8B5CF6",
    description: "自定义审批流程节点",
    defaultSize: { width: 140, height: 80 },
    defaultProps: {
      style: {
        borderRadius: 8,
        fill: "#8B5CF6",
        stroke: "#7C3AED",
        strokeWidth: 2,
      },
      approvers: [],
      approvalType: "sequential",
    },
    allowedConnections: {
      input: ["*"],
      output: ["*"],
    },
    validationRules: [],
  },
]);

// 模拟表格数据
const tableData = ref([
  {
    id: 1,
    name: "用户注册",
    description: "处理用户注册请求",
    type: "process",
    category: "user",
    priority: "high",
  },
  {
    id: 2,
    name: "邮件验证",
    description: "发送验证邮件",
    type: "notification",
    category: "email",
    priority: "medium",
  },
  {
    id: 3,
    name: "权限检查",
    description: "检查用户权限",
    type: "decision",
    category: "security",
    priority: "high",
  },
  {
    id: 4,
    name: "数据存储",
    description: "保存用户数据到数据库",
    type: "database",
    category: "data",
    priority: "high",
  },
  {
    id: 5,
    name: "第三方API调用",
    description: "调用外部服务API",
    type: "api",
    category: "integration",
    priority: "medium",
  },
  {
    id: 6,
    name: "定时任务",
    description: "定期执行的任务",
    type: "timer",
    category: "schedule",
    priority: "low",
  },
]);

// 计算属性
const nodeCount = computed(() => {
  return flowData.value?.nodes?.length || 0;
});

const edgeCount = computed(() => {
  return flowData.value?.edges?.length || 0;
});

// 方法
const loadSampleData = () => {
  // 创建示例流程数据
  const sampleData = {
    nodes: [
      {
        id: "start-1",
        type: "start",
        label: "开始",
        position: { x: 100, y: 100 },
        size: { width: 100, height: 60 },
        data: {},
        style: { fill: "#4CAF50", stroke: "#388E3C" },
      },
      {
        id: "process-1",
        type: "process",
        label: "用户注册",
        position: { x: 300, y: 100 },
        size: { width: 120, height: 80 },
        data: { timeout: 30000 },
        style: { fill: "#2196F3", stroke: "#1976D2" },
      },
      {
        id: "decision-1",
        type: "decision",
        label: "验证通过?",
        position: { x: 500, y: 100 },
        size: { width: 100, height: 100 },
        data: { conditions: ["email_verified", "phone_verified"] },
        style: { fill: "#FF9800", stroke: "#F57C00" },
      },
      {
        id: "process-2",
        type: "process",
        label: "发送欢迎邮件",
        position: { x: 700, y: 50 },
        size: { width: 120, height: 80 },
        data: { template: "welcome_email" },
        style: { fill: "#2196F3", stroke: "#1976D2" },
      },
      {
        id: "process-3",
        type: "process",
        label: "发送验证邮件",
        position: { x: 700, y: 200 },
        size: { width: 120, height: 80 },
        data: { template: "verification_email" },
        style: { fill: "#2196F3", stroke: "#1976D2" },
      },
      {
        id: "end-1",
        type: "end",
        label: "结束",
        position: { x: 900, y: 100 },
        size: { width: 100, height: 60 },
        data: {},
        style: { fill: "#F44336", stroke: "#D32F2F" },
      },
    ],
    edges: [
      {
        id: "edge-1",
        source: "start-1",
        target: "process-1",
        label: "",
        data: {},
        style: { stroke: "#333333", strokeWidth: 2 },
      },
      {
        id: "edge-2",
        source: "process-1",
        target: "decision-1",
        label: "",
        data: {},
        style: { stroke: "#333333", strokeWidth: 2 },
      },
      {
        id: "edge-3",
        source: "decision-1",
        target: "process-2",
        label: "是",
        data: { condition: "true" },
        style: { stroke: "#4CAF50", strokeWidth: 2 },
      },
      {
        id: "edge-4",
        source: "decision-1",
        target: "process-3",
        label: "否",
        data: { condition: "false" },
        style: { stroke: "#F44336", strokeWidth: 2 },
      },
      {
        id: "edge-5",
        source: "process-2",
        target: "end-1",
        label: "",
        data: {},
        style: { stroke: "#333333", strokeWidth: 2 },
      },
      {
        id: "edge-6",
        source: "process-3",
        target: "process-1",
        label: "重试",
        data: {},
        style: { stroke: "#FF9800", strokeWidth: 2 },
      },
    ],
    metadata: {
      version: "1.0.0",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      description: "用户注册流程示例",
    },
  };

  flowData.value = sampleData;
};

const handleTableRowDrag = (item: any, event: DragEvent) => {
  // 设置拖拽数据
  const dragData = {
    type: "node",
    nodeType: item.type,
    data: {
      ...item,
      sourceType: "table",
    },
    source: item,
  };

  if (event.dataTransfer) {
    event.dataTransfer.setData("application/json", JSON.stringify(dragData));
    event.dataTransfer.effectAllowed = "copy";
  }
};

const handleNodeClick = (node: FlowNode) => {
  console.log("Node clicked:", node);
};

const handleEdgeClick = (edge: FlowEdge) => {
  console.log("Edge clicked:", edge);
};

const handleValidationChange = (results: ValidationResult[]) => {
  validationResults.value = results;
  console.log("Validation results:", results);
};
</script>

<style scoped>
.flow-editor-demo {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* 拖拽样式 */
[draggable="true"] {
  cursor: grab;
}

[draggable="true"]:active {
  cursor: grabbing;
}
</style>
