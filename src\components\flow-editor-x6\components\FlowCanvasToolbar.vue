<template>
  <div
    class="flow-canvas-toolbar flex items-center gap-2 p-2 bg-white border-b border-gray-200"
  >
    <!-- 撤销/重做 -->
    <div class="flex items-center gap-1">
      <Button
        icon="pi pi-undo"
        severity="secondary"
        text
        size="small"
        :disabled="!canUndo"
        @click="handleUndo"
        v-tooltip.bottom="'撤销 (Ctrl+Z)'"
      />
      <Button
        icon="pi pi-refresh"
        severity="secondary"
        text
        size="small"
        :disabled="!canRedo"
        @click="handleRedo"
        v-tooltip.bottom="'重做 (Ctrl+Y)'"
      />
    </div>

    <Divider layout="vertical" />

    <!-- 缩放控制 -->
    <div class="flex items-center gap-1">
      <Button
        icon="pi pi-minus"
        severity="secondary"
        text
        size="small"
        @click="handleZoomOut"
        v-tooltip.bottom="'缩小'"
      />
      <span class="text-sm text-gray-600 min-w-12 text-center">
        {{ Math.round(zoomLevel * 100) }}%
      </span>
      <Button
        icon="pi pi-plus"
        severity="secondary"
        text
        size="small"
        @click="handleZoomIn"
        v-tooltip.bottom="'放大'"
      />
      <Button
        icon="pi pi-expand"
        severity="secondary"
        text
        size="small"
        @click="handleZoomToFit"
        v-tooltip.bottom="'适应画布'"
      />
      <Button
        icon="pi pi-search"
        severity="secondary"
        text
        size="small"
        @click="handleZoomToActual"
        v-tooltip.bottom="'实际大小'"
      />
    </div>

    <Divider layout="vertical" />

    <!-- 布局控制 -->
    <div class="flex items-center gap-1">
      <Button
        icon="pi pi-th-large"
        severity="secondary"
        text
        size="small"
        @click="handleAutoLayout"
        v-tooltip.bottom="'自动布局'"
      />
      <Button
        icon="pi pi-align-center"
        severity="secondary"
        text
        size="small"
        @click="handleAlignCenter"
        v-tooltip.bottom="'居中对齐'"
      />
    </div>

    <Divider layout="vertical" />

    <!-- 网格和对齐 -->
    <div class="flex items-center gap-1">
      <Button
        :icon="showGrid ? 'pi pi-th-large' : 'pi pi-th-large'"
        :severity="showGrid ? 'primary' : 'secondary'"
        text
        size="small"
        @click="handleToggleGrid"
        v-tooltip.bottom="'切换网格'"
      />
      <Button
        :icon="snapToGrid ? 'pi pi-lock' : 'pi pi-unlock'"
        :severity="snapToGrid ? 'primary' : 'secondary'"
        text
        size="small"
        @click="handleToggleSnap"
        v-tooltip.bottom="'切换对齐'"
      />
    </div>

    <Divider layout="vertical" />

    <!-- 选择和操作 -->
    <div class="flex items-center gap-1">
      <Button
        icon="pi pi-copy"
        severity="secondary"
        text
        size="small"
        :disabled="!hasSelection"
        @click="handleCopy"
        v-tooltip.bottom="'复制 (Ctrl+C)'"
      />
      <Button
        icon="pi pi-clone"
        severity="secondary"
        text
        size="small"
        :disabled="!hasClipboard"
        @click="handlePaste"
        v-tooltip.bottom="'粘贴 (Ctrl+V)'"
      />
      <Button
        icon="pi pi-trash"
        severity="danger"
        text
        size="small"
        :disabled="!hasSelection"
        @click="handleDelete"
        v-tooltip.bottom="'删除 (Delete)'"
      />
    </div>

    <div class="flex-1" />

    <!-- 右侧信息 -->
    <div class="flex items-center gap-4 text-sm text-gray-600">
      <span>节点: {{ nodeCount }}</span>
      <span>连线: {{ edgeCount }}</span>
      <span v-if="hasSelection">已选择: {{ selectionCount }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, onUnmounted } from "vue";
import { Graph } from "@antv/x6";
import Button from "primevue/button";
import Divider from "primevue/divider";

// Props
interface Props {
  graph?: Graph;
}

const props = defineProps<Props>();

// Inject store if available
const flowStore = inject("flowStore", null) as any;

// Reactive state
const zoomLevel = ref(1);
const showGrid = ref(true);
const snapToGrid = ref(true);
const canUndo = ref(false);
const canRedo = ref(false);
const hasSelection = ref(false);
const hasClipboard = ref(false);
const nodeCount = ref(0);
const edgeCount = ref(0);
const selectionCount = ref(0);

// Computed
const graph = computed(() => props.graph);

// Methods
const handleUndo = () => {
  if (graph.value && graph.value.canUndo()) {
    graph.value.undo();
    updateState();
  }
};

const handleRedo = () => {
  if (graph.value && graph.value.canRedo()) {
    graph.value.redo();
    updateState();
  }
};

const handleZoomIn = () => {
  if (graph.value) {
    graph.value.zoom(0.1);
  }
};

const handleZoomOut = () => {
  if (graph.value) {
    graph.value.zoom(-0.1);
  }
};

const handleZoomToFit = () => {
  if (graph.value) {
    graph.value.zoomToFit({ padding: 20 });
  }
};

const handleZoomToActual = () => {
  if (graph.value) {
    graph.value.scale(1);
    graph.value.centerContent();
  }
};

const handleAutoLayout = () => {
  // TODO: 实现自动布局算法
  console.log("Auto layout");
};

const handleAlignCenter = () => {
  if (graph.value) {
    graph.value.centerContent();
  }
};

const handleToggleGrid = () => {
  if (graph.value) {
    showGrid.value = !showGrid.value;
    graph.value.showGrid(showGrid.value);
  }
};

const handleToggleSnap = () => {
  snapToGrid.value = !snapToGrid.value;
  // TODO: 实现对齐功能
};

const handleCopy = () => {
  if (graph.value && hasSelection.value) {
    const selectedCells = graph.value.getSelectedCells();
    if (selectedCells.length > 0) {
      graph.value.copy(selectedCells);
      hasClipboard.value = true;
    }
  }
};

const handlePaste = () => {
  if (graph.value && hasClipboard.value) {
    graph.value.paste({ offset: 32 });
    updateState();
  }
};

const handleDelete = () => {
  if (graph.value && hasSelection.value) {
    const selectedCells = graph.value.getSelectedCells();
    if (selectedCells.length > 0) {
      graph.value.removeCells(selectedCells);
      updateState();
    }
  }
};

// Update state based on graph events
const updateState = () => {
  if (!graph.value) return;

  // Update zoom level
  zoomLevel.value = graph.value.zoom();

  // Update history state
  canUndo.value = graph.value.canUndo();
  canRedo.value = graph.value.canRedo();

  // Update selection state
  const selectedCells = graph.value.getSelectedCells();
  hasSelection.value = selectedCells.length > 0;
  selectionCount.value = selectedCells.length;

  // Update counts
  nodeCount.value = graph.value.getNodes().length;
  edgeCount.value = graph.value.getEdges().length;
};

// Keyboard shortcuts
const handleKeydown = (e: KeyboardEvent) => {
  if (!graph.value) return;

  const { ctrlKey, metaKey, key } = e;
  const isCtrl = ctrlKey || metaKey;

  if (isCtrl) {
    switch (key.toLowerCase()) {
      case "z":
        e.preventDefault();
        if (e.shiftKey) {
          handleRedo();
        } else {
          handleUndo();
        }
        break;
      case "y":
        e.preventDefault();
        handleRedo();
        break;
      case "c":
        e.preventDefault();
        handleCopy();
        break;
      case "v":
        e.preventDefault();
        handlePaste();
        break;
    }
  } else if (key === "Delete" || key === "Backspace") {
    e.preventDefault();
    handleDelete();
  }
};

// Lifecycle
onMounted(() => {
  if (graph.value) {
    // Bind events
    graph.value.on("scale", updateState);
    graph.value.on("translate", updateState);
    graph.value.on("selection:changed", updateState);
    graph.value.on("cell:added", updateState);
    graph.value.on("cell:removed", updateState);
    graph.value.on("history:change", updateState);

    // Initial state update
    updateState();
  }

  // Bind keyboard events
  document.addEventListener("keydown", handleKeydown);
});

onUnmounted(() => {
  if (graph.value) {
    graph.value.off("scale", updateState);
    graph.value.off("translate", updateState);
    graph.value.off("selection:changed", updateState);
    graph.value.off("cell:added", updateState);
    graph.value.off("cell:removed", updateState);
    graph.value.off("history:change", updateState);
  }

  document.removeEventListener("keydown", handleKeydown);
});
</script>

<style scoped>
.flow-canvas-toolbar {
  user-select: none;
}
</style>
