<template>
  <div class="simple-toolbar flex items-center gap-2 p-2 bg-white border-b border-gray-200">
    <!-- 基本信息 -->
    <div class="flex items-center gap-4 text-sm text-gray-600">
      <span>节点: {{ nodeCount }}</span>
      <span>连线: {{ edgeCount }}</span>
      <span v-if="hasSelection">已选择: {{ selectionCount }}</span>
    </div>

    <div class="flex-1" />

    <!-- 基本操作按钮 -->
    <div class="flex items-center gap-1">
      <button
        class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        @click="handleZoomIn"
        title="放大"
      >
        放大
      </button>
      <button
        class="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
        @click="handleZoomOut"
        title="缩小"
      >
        缩小
      </button>
      <button
        class="px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
        @click="handleZoomToFit"
        title="适应画布"
      >
        适应画布
      </button>
      <button
        class="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
        @click="handleClearSelection"
        title="清除选择"
      >
        清除选择
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { Graph } from '@antv/x6'

// Props
interface Props {
  graph?: Graph
}

const props = defineProps<Props>()

// Reactive state
const nodeCount = ref(0)
const edgeCount = ref(0)
const hasSelection = ref(false)
const selectionCount = ref(0)

// Computed
const graph = computed(() => props.graph)

// Methods
const handleZoomIn = () => {
  if (graph.value) {
    graph.value.zoom(0.1)
  }
}

const handleZoomOut = () => {
  if (graph.value) {
    graph.value.zoom(-0.1)
  }
}

const handleZoomToFit = () => {
  if (graph.value) {
    graph.value.zoomToFit({ padding: 20 })
  }
}

const handleClearSelection = () => {
  if (graph.value) {
    graph.value.cleanSelection()
  }
}

// Update state
const updateState = () => {
  if (!graph.value) return

  try {
    // Update counts
    nodeCount.value = graph.value.getNodes().length
    edgeCount.value = graph.value.getEdges().length

    // Update selection state (simplified)
    hasSelection.value = false
    selectionCount.value = 0
  } catch (error) {
    console.warn('Error updating toolbar state:', error)
  }
}

// Keyboard shortcuts
const handleKeydown = (e: KeyboardEvent) => {
  if (!graph.value) return

  const { ctrlKey, metaKey, key } = e
  const isCtrl = ctrlKey || metaKey

  if (isCtrl) {
    switch (key.toLowerCase()) {
      case '=':
      case '+':
        e.preventDefault()
        handleZoomIn()
        break
      case '-':
        e.preventDefault()
        handleZoomOut()
        break
      case '0':
        e.preventDefault()
        handleZoomToFit()
        break
    }
  } else if (key === 'Escape') {
    e.preventDefault()
    handleClearSelection()
  }
}

// Watch for graph changes
watch(
  () => graph.value,
  (newGraph, oldGraph) => {
    // Unbind old graph events
    if (oldGraph) {
      oldGraph.off('cell:added', updateState)
      oldGraph.off('cell:removed', updateState)
    }

    // Bind new graph events
    if (newGraph) {
      newGraph.on('cell:added', updateState)
      newGraph.on('cell:removed', updateState)

      // Initial state update
      updateState()
    }
  },
  { immediate: true }
)

// Lifecycle
onMounted(() => {
  // Bind keyboard events
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  // Unbind graph events
  if (graph.value) {
    graph.value.off('cell:added', updateState)
    graph.value.off('cell:removed', updateState)
  }

  // Unbind keyboard events
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.simple-toolbar {
  user-select: none;
}
</style>
