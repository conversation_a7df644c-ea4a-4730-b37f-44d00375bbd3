<template>
  <div class="flow-editor-container w-full h-full flex">
    <!-- 左侧控制面板 -->
    <FlowControlPanel
      class="w-80 border-r border-gray-200 bg-gray-50"
      :node-types="nodeTypes"
      @drag-start="handleDragStart"
    />

    <!-- 中间画布区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 工具栏 -->
      <FlowToolbar
        class="h-12 border-b border-gray-200 bg-white"
        @save="handleSave"
        @load="handleLoad"
        @clear="handleClear"
        @validate="handleValidate"
      />

      <!-- 画布操作工具栏 -->
      <SimpleToolbar
        class="h-12 border-b border-gray-200 bg-gray-50"
        :graph="graph"
      />

      <!-- 画布 -->
      <div class="flex-1 relative">
        <div ref="containerRef" class="w-full h-full" />

        <!-- 验证结果覆盖层 -->
        <FlowValidationOverlay
          v-if="validationResults.length > 0"
          :results="validationResults"
          @close="clearValidation"
        />

        <!-- 连线标签编辑器 -->
        <EdgeLabelEditor
          :visible="labelEditor.visible"
          :position="labelEditor.position"
          :initial-value="labelEditor.initialValue"
          @save="handleLabelSave"
          @cancel="handleLabelCancel"
        />
      </div>
    </div>

    <!-- 右侧属性面板 -->
    <FlowPropertyPanel
      class="w-80 border-l border-gray-200 bg-gray-50"
      :selected-node="selectedNode"
      :selected-edge="selectedEdge"
      @update-node="handleUpdateNode"
      @update-edge="handleUpdateEdge"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, provide } from "vue";
import { Graph } from "@antv/x6";
import { useFlowStore } from "./store/useFlowStore";
import { useFlowGraph } from "./composables/useFlowGraph";
import { useFlowValidation } from "./composables/useFlowValidation";
import { useFlowDragDrop } from "./composables/useFlowDragDrop";
import { initializeNodeTypes } from "./registry/initializeNodeTypes";
import FlowControlPanel from "./components/FlowControlPanel.vue";
import FlowToolbar from "./components/FlowToolbar.vue";
import SimpleToolbar from "./components/SimpleToolbar.vue";
import FlowPropertyPanel from "./components/FlowPropertyPanel.vue";
import FlowValidationOverlay from "./components/FlowValidationOverlay.vue";
import EdgeLabelEditor from "./components/EdgeLabelEditor.vue";
import type {
  FlowNodeType,
  FlowNode,
  FlowEdge,
  ValidationResult,
} from "./types";

// Props
interface Props {
  modelValue?: any;
  readonly?: boolean;
  nodeTypes?: FlowNodeType[];
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  nodeTypes: () => [],
});

// Emits
const emit = defineEmits<{
  "update:modelValue": [value: any];
  "node-click": [node: FlowNode];
  "edge-click": [edge: FlowEdge];
  "validation-change": [results: ValidationResult[]];
}>();

// Refs
const containerRef = ref<HTMLDivElement>();

// Label editor state
const labelEditor = ref({
  visible: false,
  position: { x: 0, y: 0 },
  initialValue: "",
  edgeId: "",
});

// Store
const flowStore = useFlowStore();
provide("flowStore", flowStore);

// Composables
const { graph, initGraph, destroyGraph } = useFlowGraph(containerRef);
const { validateFlow, validationResults, clearValidation } =
  useFlowValidation(flowStore);
const { handleDragStart } = useFlowDragDrop(graph, flowStore);

// State
const selectedNode = ref<FlowNode | null>(null);
const selectedEdge = ref<FlowEdge | null>(null);
const nodeTypes = ref<FlowNodeType[]>(props.nodeTypes);

// Methods
const handleSave = () => {
  const data = flowStore.exportData();
  emit("update:modelValue", data);
};

const handleLoad = (data: any) => {
  flowStore.importData(data);
};

const handleClear = () => {
  flowStore.clearAll();
  selectedNode.value = null;
  selectedEdge.value = null;
};

const handleValidate = async () => {
  const results = await validateFlow();
  emit("validation-change", results);
};

const handleUpdateNode = (nodeData: Partial<FlowNode>) => {
  if (selectedNode.value) {
    flowStore.updateNode(selectedNode.value.id, nodeData);
  }
};

const handleUpdateEdge = (edgeData: Partial<FlowEdge>) => {
  if (selectedEdge.value) {
    flowStore.updateEdge(selectedEdge.value.id, edgeData);
  }
};

// Label editor methods
const showLabelEditor = (
  edgeId: string,
  position: { x: number; y: number },
  currentLabel: string = ""
) => {
  labelEditor.value = {
    visible: true,
    position,
    initialValue: currentLabel,
    edgeId,
  };
};

const handleLabelSave = (value: string) => {
  if (labelEditor.value.edgeId) {
    flowStore.updateEdge(labelEditor.value.edgeId, { label: value });

    // Update the graph edge label
    const edge = graph.value?.getCellById(labelEditor.value.edgeId);
    if (edge && edge.isEdge()) {
      edge.setLabels([
        {
          attrs: {
            text: {
              text: value,
              fill: "#333",
              fontSize: 12,
              textAnchor: "middle",
            },
            rect: {
              ref: "text",
              fill: "white",
              stroke: "#ccc",
              strokeWidth: 1,
              rx: 3,
              ry: 3,
              refWidth: "100%",
              refHeight: "100%",
              refX: -5,
              refY: -5,
              refWidth2: 10,
              refHeight2: 10,
            },
          },
          position: 0.5,
        },
      ]);
    }
  }
  labelEditor.value.visible = false;
};

const handleLabelCancel = () => {
  labelEditor.value.visible = false;
};

// Lifecycle
onMounted(async () => {
  // 初始化节点类型
  initializeNodeTypes();

  await initGraph();

  // 监听节点选择
  graph.value?.on("node:click", ({ node }) => {
    const nodeData = flowStore.getNode(node.id);
    selectedNode.value = nodeData || null;
    selectedEdge.value = null;
    if (nodeData) {
      emit("node-click", nodeData);
    }
  });

  // 监听边选择
  graph.value?.on("edge:click", ({ edge }) => {
    const edgeData = flowStore.getEdge(edge.id);
    selectedEdge.value = edgeData || null;
    selectedNode.value = null;
    if (edgeData) {
      emit("edge-click", edgeData);
    }
  });

  // 监听边双击编辑标签
  graph.value?.on("edge:dblclick", ({ edge, e }) => {
    const edgeData = flowStore.getEdge(edge.id);
    if (edgeData) {
      const rect = containerRef.value?.getBoundingClientRect();
      if (rect) {
        showLabelEditor(
          edge.id,
          {
            x: e.clientX - rect.left,
            y: e.clientY - rect.top,
          },
          edgeData.label || ""
        );
      }
    }
  });

  // 监听画布点击
  graph.value?.on("blank:click", () => {
    selectedNode.value = null;
    selectedEdge.value = null;
    labelEditor.value.visible = false;
  });
});

onUnmounted(() => {
  destroyGraph();
});
</script>

<style scoped>
.flow-editor-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}
</style>
