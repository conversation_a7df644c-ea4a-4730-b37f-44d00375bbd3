import { ref, type Ref } from "vue";
import { Graph, Node, Edge } from "@antv/x6";
import type { FlowNode, FlowEdge } from "../types";

export function useFlowGraph(containerRef: Ref<HTMLDivElement | undefined>) {
  const graph = ref<Graph>();

  /**
   * 初始化图形
   */
  const initGraph = async () => {
    if (!containerRef.value) {
      throw new Error("Container element is required");
    }

    graph.value = new Graph({
      container: containerRef.value,
      width: containerRef.value.clientWidth,
      height: containerRef.value.clientHeight,

      // 网格配置
      grid: {
        size: 20,
        visible: true,
        type: "doubleMesh",
        args: [
          {
            color: "#E7E8EA",
            thickness: 1,
          },
          {
            color: "#CBCED3",
            thickness: 1,
            factor: 4,
          },
        ],
      },

      // 对齐线
      snapline: {
        enabled: true,
        sharp: true,
      },

      // 连线配置
      connecting: {
        router: "manhattan",
        connector: {
          name: "rounded",
          args: {
            radius: 8,
          },
        },
        anchor: "center",
        connectionPoint: "anchor",
        allowBlank: false,
        allowLoop: false,
        allowNode: true,
        allowEdge: false,
        allowMulti: false,
        snap: {
          radius: 20,
        },
        createEdge() {
          return new Edge({
            attrs: {
              line: {
                stroke: "#A2B1C3",
                strokeWidth: 2,
                targetMarker: {
                  name: "block",
                  width: 12,
                  height: 8,
                },
              },
            },
            labels: [
              {
                attrs: {
                  text: {
                    text: "",
                    fill: "#333",
                    fontSize: 12,
                    textAnchor: "middle",
                  },
                  rect: {
                    ref: "text",
                    fill: "white",
                    stroke: "#ccc",
                    strokeWidth: 1,
                    rx: 3,
                    ry: 3,
                    refWidth: "100%",
                    refHeight: "100%",
                    refX: -5,
                    refY: -5,
                    refWidth2: 10,
                    refHeight2: 10,
                  },
                },
                position: 0.5,
              },
            ],
            zIndex: 0,
          });
        },
        validateConnection({
          targetMagnet,
          sourceMagnet,
          sourceView,
          targetView,
        }) {
          // 不允许连接到自己
          if (sourceView === targetView) {
            return false;
          }

          // 检查端口类型匹配
          if (targetMagnet && sourceMagnet) {
            const sourcePort = sourceMagnet.getAttribute("port-group");
            const targetPort = targetMagnet.getAttribute("port-group");

            // 输出端口只能连接到输入端口
            if (sourcePort === "output" && targetPort === "input") {
              return true;
            }
          }

          return !!targetMagnet;
        },
      },

      // 高亮配置
      highlighting: {
        magnetAdsorbed: {
          name: "stroke",
          args: {
            attrs: {
              fill: "#5F95FF",
              stroke: "#5F95FF",
            },
          },
        },
      },

      // 缩放配置
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: "ctrl",
        minScale: 0.1,
        maxScale: 3,
      },

      // 选择配置
      selecting: {
        enabled: true,
        rubberband: true,
        movable: true,
        showNodeSelectionBox: true,
      },

      // 剪贴板
      clipboard: {
        enabled: true,
      },

      // 键盘快捷键
      keyboard: {
        enabled: true,
      },

      // 历史记录
      history: {
        enabled: true,
      },

      // 小地图
      minimap: {
        enabled: false,
        container: undefined,
      },

      // 滚动
      scroller: {
        enabled: true,
        pannable: true,
        cursor: "grab",
        autoResize: true,
      },
    });

    // 注册自定义节点
    registerCustomNodes();

    // 绑定事件
    bindEvents();

    // 监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      if (graph.value && containerRef.value) {
        graph.value.resize(
          containerRef.value.clientWidth,
          containerRef.value.clientHeight
        );
      }
    });
    resizeObserver.observe(containerRef.value);
  };

  /**
   * 注册自定义节点
   */
  const registerCustomNodes = () => {
    if (!graph.value) return;

    // 注册圆角矩形节点
    Graph.registerNode(
      "flow-rect",
      {
        inherit: "rect",
        width: 120,
        height: 60,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            rx: 8,
            ry: 8,
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            input: {
              position: "left",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#31d0c6",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                  "port-group": "input",
                },
              },
            },
            output: {
              position: "right",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#fe854f",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                  "port-group": "output",
                },
              },
            },
          },
          items: [
            {
              id: "input",
              group: "input",
            },
            {
              id: "output",
              group: "output",
            },
          ],
        },
      },
      true
    );

    // 注册圆形节点
    Graph.registerNode(
      "flow-circle",
      {
        inherit: "circle",
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            input: {
              position: "left",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#31d0c6",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                  "port-group": "input",
                },
              },
            },
            output: {
              position: "right",
              attrs: {
                circle: {
                  r: 6,
                  magnet: true,
                  stroke: "#fe854f",
                  strokeWidth: 2,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                  "port-group": "output",
                },
              },
            },
          },
          items: [
            {
              id: "input",
              group: "input",
            },
            {
              id: "output",
              group: "output",
            },
          ],
        },
      },
      true
    );

    // 注册菱形节点
    Graph.registerNode(
      "flow-polygon",
      {
        inherit: "polygon",
        width: 80,
        height: 80,
        attrs: {
          body: {
            strokeWidth: 2,
            stroke: "#5F95FF",
            fill: "#EFF4FF",
            refPoints: "0,10 10,0 20,10 10,20",
          },
          text: {
            fontSize: 12,
            fill: "#262626",
            textAnchor: "middle",
            textVerticalAnchor: "middle",
          },
        },
        ports: {
          groups: {
            top: {
              position: "top",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            right: {
              position: "right",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            bottom: {
              position: "bottom",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
            left: {
              position: "left",
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: "#5F95FF",
                  strokeWidth: 1,
                  fill: "#fff",
                  style: {
                    visibility: "hidden",
                  },
                },
              },
            },
          },
          items: [
            {
              group: "top",
            },
            {
              group: "right",
            },
            {
              group: "bottom",
            },
            {
              group: "left",
            },
          ],
        },
      },
      true
    );
  };

  /**
   * 绑定事件
   */
  const bindEvents = () => {
    if (!graph.value) return;

    // 鼠标悬停显示连接桩
    graph.value.on("node:mouseenter", ({ node }) => {
      const container = containerRef.value;
      if (container) {
        const ports = container.querySelectorAll(
          `[data-cell-id="${node.id}"] .x6-port-body`
        );
        showPorts(ports, true);
      }
    });

    graph.value.on("node:mouseleave", ({ node }) => {
      const container = containerRef.value;
      if (container) {
        const ports = container.querySelectorAll(
          `[data-cell-id="${node.id}"] .x6-port-body`
        );
        showPorts(ports, false);
      }
    });

    // 连线创建事件
    graph.value.on("edge:connected", ({ edge }) => {
      console.log("Edge connected:", edge);
      // 可以在这里添加连线验证逻辑
    });

    // 端口高亮
    graph.value.on("node:port:mouseenter", ({ port }) => {
      const portElement = document.querySelector(
        `[port="${port}"] .x6-port-body`
      ) as HTMLElement;
      if (portElement) {
        portElement.style.fill = "#31d0c6";
        portElement.style.stroke = "#31d0c6";
      }
    });

    graph.value.on("node:port:mouseleave", ({ port }) => {
      const portElement = document.querySelector(
        `[port="${port}"] .x6-port-body`
      ) as HTMLElement;
      if (portElement) {
        portElement.style.fill = "#fff";
        portElement.style.stroke = "#5F95FF";
      }
    });
  };

  /**
   * 显示/隐藏连接桩
   */
  const showPorts = (ports: NodeListOf<Element>, show: boolean) => {
    for (let i = 0, len = ports.length; i < len; i++) {
      const port = ports[i] as HTMLElement;
      port.style.visibility = show ? "visible" : "hidden";
    }
  };

  /**
   * 添加节点到图形
   */
  const addNodeToGraph = (flowNode: FlowNode) => {
    if (!graph.value) return;

    // 根据节点类型配置端口
    const ports = getNodePorts(flowNode.type);

    const node = graph.value.addNode({
      id: flowNode.id,
      shape: getNodeShape(flowNode.type),
      x: flowNode.position.x,
      y: flowNode.position.y,
      width: flowNode.size.width,
      height: flowNode.size.height,
      label: flowNode.label,
      data: flowNode.data,
      attrs: {
        body: flowNode.style || {},
      },
      ports: ports,
    });

    return node;
  };

  /**
   * 添加边到图形
   */
  const addEdgeToGraph = (flowEdge: FlowEdge) => {
    if (!graph.value) return;

    const edge = graph.value.addEdge({
      id: flowEdge.id,
      source: flowEdge.source,
      target: flowEdge.target,
      label: flowEdge.label,
      data: flowEdge.data,
      attrs: {
        line: flowEdge.style,
      },
    });

    return edge;
  };

  /**
   * 根据节点类型获取形状
   */
  const getNodeShape = (nodeType: string): string => {
    switch (nodeType) {
      case "start":
      case "end":
      case "timer":
        return "flow-circle";
      case "decision":
        return "flow-polygon";
      default:
        return "flow-rect";
    }
  };

  /**
   * 根据节点类型获取端口配置
   */
  const getNodePorts = (nodeType: string) => {
    switch (nodeType) {
      case "start":
        return [
          {
            id: "output",
            group: "output",
          },
        ];
      case "end":
        return [
          {
            id: "input",
            group: "input",
          },
        ];
      case "decision":
        return [
          {
            id: "input",
            group: "input",
          },
          {
            id: "output-yes",
            group: "output",
          },
          {
            id: "output-no",
            group: "output",
          },
        ];
      default:
        return [
          {
            id: "input",
            group: "input",
          },
          {
            id: "output",
            group: "output",
          },
        ];
    }
  };

  /**
   * 销毁图形
   */
  const destroyGraph = () => {
    if (graph.value) {
      graph.value.dispose();
      graph.value = undefined;
    }
  };

  /**
   * 缩放到适合大小
   */
  const zoomToFit = () => {
    if (graph.value) {
      graph.value.zoomToFit({ padding: 20 });
    }
  };

  /**
   * 居中显示
   */
  const centerContent = () => {
    if (graph.value) {
      graph.value.centerContent();
    }
  };

  return {
    graph,
    initGraph,
    destroyGraph,
    addNodeToGraph,
    addEdgeToGraph,
    zoomToFit,
    centerContent,
  };
}
